{% extends "base.html" %}

{% block title %}Add New Primary Care Provider{% endblock %}

{% block content %}
{% load static %}
<!-- Link to the external stylesheet -->
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/staff_form.css' %}">

<div class="form-container">
    <h2 class="subheading">Add a New Primary Care Provider</h2>
    <div class="alert alert-info">
        <small><em style="color: #6c757d;">Note: Fields marked with an asterisk (*) are required.</em></small>
    </div>
    <form method="post">
        {% csrf_token %}

        <!-- Display Errors for Primary Care Provider Form -->
        {% if physician_form.errors %}
            <div class="alert alert-danger">
                <ul>
                    {% for field, errors in physician_form.errors.items %}
                        <li>{{ field }}: {{ errors|join:", " }}</li>
                    {% endfor %}
                </ul>
            </div>
        {% endif %}
        
        <!-- Display Errors for Clinic Primary Care Provider Formset -->
        {% for form in clinic_formset %}
            {% if form.errors %}
                <div class="alert alert-danger">
                    <strong>Errors in Clinic Association {{ forloop.counter }}:</strong>
                    <ul>
                        {% for field, errors in form.errors.items %}
                            <li>{{ field }}: {{ errors|join:", " }}</li>
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}
        {% endfor %}

        <!-- Primary Care Provider Details Section -->
        <h3>1. Primary Care Provider Information</h3>
        <hr>

        <div class="form-group">
            <label for="{{ physician_form.last_name.id_for_label }}">{{ physician_form.last_name.label }}: <span class="text-danger">*</span></label>
            {{ physician_form.last_name }}
            {% if physician_form.last_name.errors %}
            <div class="text-danger">{{ physician_form.last_name.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ physician_form.first_name.id_for_label }}">{{ physician_form.first_name.label }}: <span class="text-danger">*</span></label>
            {{ physician_form.first_name }}
            {% if physician_form.first_name.errors %}
            <div class="text-danger">{{ physician_form.first_name.errors }}</div>
            {% endif %}
        </div>

        <div class="form-group">
            <label for="{{ physician_form.title.id_for_label }}">{{ physician_form.title.label }}: <span class="text-danger">*</span></label><br>
            {% for radio in physician_form.title %}
                <div class="form-check form-check-inline">
                    {{ radio.tag }}
                    <label class="form-check-label">{{ radio.choice_label }}</label>
                </div>
            {% endfor %}
        </div>

        <div class="form-group">
            <label for="{{ physician_form.practitioner_id.id_for_label }}">{{ physician_form.practitioner_id.label }}:</label>
            {{ physician_form.practitioner_id }}
        </div>

        <div class="form-group">
            <label for="{{ physician_form.gender.id_for_label }}">{{ physician_form.gender.label }}:</label>
            {{ physician_form.gender }}
            {% if physician_form.gender.errors %}
            <div class="text-danger">{{ physician_form.gender.errors }}</div>
            {% endif %}
        </div>

        <div class="form-group">
            <label for="{{ physician_form.primary_email.id_for_label }}">{{ physician_form.primary_email.label }}:</label>
            {{ physician_form.primary_email }}
        </div>

        <div class="form-group">
            <label for="{{ physician_form.primary_phone.id_for_label }}">{{ physician_form.primary_phone.label }}:</label>
            {{ physician_form.primary_phone }}
        </div>

        <div class="form-group">
            <label for="{{ physician_form.alternate_email.id_for_label }}">{{ physician_form.alternate_email.label }}:</label>
            {{ physician_form.alternate_email }}
        </div>

        <div class="form-group">
            <label for="{{ physician_form.alternate_phone.id_for_label }}">{{ physician_form.alternate_phone.label }}:</label>
            {{ physician_form.alternate_phone }}
        </div>

        <div class="form-group">
            <label>Email Opt-out:</label>
            <div style="margin-top: 5px;">
                <input type="checkbox" 
                       name="do_not_email" 
                       id="id_do_not_email"
                       {% if physician_form.do_not_email.value %}checked{% endif %}>
                <label for="id_do_not_email" style="margin-left: 8px; font-weight: normal;">Check to opt out of email communications</label>
            </div>
        </div>

        <div class="form-group">
            <label for="{{ physician_form.date_signed_eopcn.id_for_label }}">{{ physician_form.date_signed_eopcn.label }}:</label>
            {{ physician_form.date_signed_eopcn }}
        </div>

        <!-- Languages Section -->
        <div class="form-group">
            <label for="languages">Languages Spoken:</label>
            <div id="languages-container">
                <div class="language-entry">
                    <div class="language-input-row">
                        <input type="text" class="form-control language-input" name="languages[]" placeholder="Enter language (e.g., English, Spanish, French)">
                        <button type="button" class="btn btn-outline-danger btn-sm remove-language" title="Remove this language">
                            Remove
                        </button>
                    </div>
                </div>
            </div>
            <div class="add-button-container">
                <button type="button" class="btn btn-success btn-sm" id="add-language" title="Add another language">
                    + Add Another Language
                </button>
            </div>
        </div>
</div>

<!-- Include the formset management form -->
{{ clinic_formset.management_form }}

<!-- All Clinic Associations in one form container -->
<div class="form-container">
    <h3>2. Clinic Associations</h3>
    <hr>
    
    <!-- Instruction note for clinic associations -->
    <div class="alert alert-info mb-3">
        <small><em style="color: #6c757d;">Note: You can add up to 3 clinic associations. Only the first one is required if the primary care provider practices at multiple clinics.</em></small>
    </div>
    
    <!-- Loop through each form in the formset -->
    {% for form in clinic_formset %}
        {% if forloop.counter > 1 %}
            <hr class="association-divider">
            <h4>Clinic Association {{ forloop.counter }}</h4>
        {% else %}
            <h4>Primary Clinic Association</h4>
        {% endif %}

        <div class="form-group">
            <label for="{{ form.clinic.id_for_label }}">{{ form.clinic.label }}: {% if forloop.counter == 1 %}<span class="text-danger">*</span>{% endif %}</label>
            {{ form.clinic }}
        </div>
        <div class="helper-text">
            <small class="form-text text-muted">ℹ️ Missing a clinic? <a href="{% url 'clinic_list' %}" target="_blank">Add clinics here</a></small>
        </div>

        <div class="form-group">
            <label for="{{ form.portion_of_practice.id_for_label }}">{{ form.portion_of_practice.label }}: {% if forloop.counter == 1 %}<span class="text-danger">*</span>{% endif %}</label><br>
            {% for radio in form.portion_of_practice %}
                <div class="form-check form-check-inline">
                    {{ radio.tag }}
                    <label class="form-check-label">{{ radio.choice_label }}</label>
                </div>
            {% endfor %}
        </div>

        <div class="form-group">
            <label for="{{ form.accepting_patients.id_for_label }}">{{ form.accepting_patients.label }}:</label><br>
            {% for radio in form.accepting_patients %}
                <div class="form-check form-check-inline">
                    {{ radio.tag }}
                    <label class="form-check-label">{{ radio.choice_label }}</label>
                </div>
            {% endfor %}
        </div>

        <div class="form-group">
            <label for="{{ form.include_on_afad_website.id_for_label }}">{{ form.include_on_afad_website.label }}:</label><br>
            {% for radio in form.include_on_afad_website %}
                <div class="form-check form-check-inline">
                    {{ radio.tag }}
                    <label class="form-check-label">{{ radio.choice_label }}</label>
                </div>
            {% endfor %}
        </div>

        <div class="form-group">
            <label for="{{ form.include_on_eopcn_website.id_for_label }}">{{ form.include_on_eopcn_website.label }}:</label><br>
            {% for radio in form.include_on_eopcn_website %}
                <div class="form-check form-check-inline">
                    {{ radio.tag }}
                    <label class="form-check-label">{{ radio.choice_label }}</label>
                </div>
            {% endfor %}
        </div>

        <div class="form-group">
            <label for="{{ form.date_active_in_clinic.id_for_label }}">{{ form.date_active_in_clinic.label }}:</label>
            {{ form.date_active_in_clinic }}
        </div>

        <!-- Add the new CPAR fields section -->
        <div class="form-section-header">
            <h5>CPAR Information</h5>
        </div>
        
        <div class="form-group">
            <label for="{{ form.CPAR_Panel_ID.id_for_label }}">CPAR Panel ID:</label>
            {{ form.CPAR_Panel_ID }}
            {% if form.CPAR_Panel_ID.errors %}
                <div class="text-danger">{{ form.CPAR_Panel_ID.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.active_CII.id_for_label }}">{{ form.active_CII.label }}:</label>
            {{ form.active_CII }}
            {% if form.active_CII.errors %}
                <div class="text-danger">{{ form.active_CII.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-group">
            <label for="{{ form.active_CPAR.id_for_label }}">{{ form.active_CPAR.label }}:</label>
            {{ form.active_CPAR }}
            {% if form.active_CPAR.errors %}
                <div class="text-danger">{{ form.active_CPAR.errors }}</div>
            {% endif %}
        </div>

    {% endfor %}
</div>

<!-- Email Group Widget -->
<div class="form-container">
    {% with form=physician_form %}
        {% include 'staff/email_group_widget.html' %}
    {% endwith %}
</div>

<!-- Submit buttons -->
<div class="form-container">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <button type="submit" class="btn btn-primary mt-3">Save</button>
        <a href="{% url 'physician_list' %}" class="btn-secondary mt-3">Back to Primary Care Provider List</a>
    </div>
</div>

</form>

<style>
    /* Add styling for the association divider */
    .association-divider {
        margin: 30px 0;
        border-top: 2px solid #dee2e6;
    }
    
    /* Remove checkbox-row styling and use standard form styling */
    .form-group input[type="checkbox"] {
        margin: 0;
        width: auto;
    }
    
    /* Form section header styling */
    .form-section-header {
        margin-top: 20px;
        margin-bottom: 15px;
    }
    
    .form-section-header h5 {
        color: #0067b1;
        border-bottom: 1px solid #ddd;
        padding-bottom: 5px;
        margin: 0;
    }
    
    /* Override any inherited styling that might affect positioning */
    .form-group .checkbox-row {
        padding-left: 0;
        margin-left: 0;
    }
    
    .form-group .checkbox-item {
        margin-left: 0;
        padding-left: 0;
    }
    
    /* Ensure consistent form group spacing */
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group > label {
        display: block;
        margin-bottom: 5px;
        font-weight: normal; /* Changed from bold to normal */
        color: #333;
        white-space: nowrap;
    }
    
    .form-group input[type="number"], 
    .form-group input[type="text"], 
    .form-group input[type="email"], 
    .form-group input[type="date"], 
    .form-group select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        box-sizing: border-box;
    }

    .form-text {
        font-size: 0.875em;
        color: #6c757d;
        margin-left: 10px;
        font-style: normal;
    }

    .helper-text {
        margin-left: 210px;
        margin-top: -10px;
        margin-bottom: 15px;
    }

    /* Languages section styling */
    .language-entry {
        margin-bottom: 10px;
    }

    .language-input-row {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .language-input {
        flex: 1;
        min-width: 0;
    }

    .remove-language {
        flex-shrink: 0;
        padding: 6px 12px;
        font-size: 0.875em;
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .remove-language:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .add-button-container {
        display: block;
        width: 100%;
        margin-top: 10px;
    }

    #add-language {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
        padding: 8px 16px;
        font-size: 0.875em;
    }

    #add-language:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const addLanguageBtn = document.getElementById('add-language');
    const languagesContainer = document.getElementById('languages-container');

    addLanguageBtn.addEventListener('click', function() {
        const languageEntry = document.createElement('div');
        languageEntry.className = 'language-entry';
        languageEntry.innerHTML = `
            <div class="language-input-row">
                <input type="text" class="form-control language-input" name="languages[]" placeholder="Enter language (e.g., English, Spanish, French)">
                <button type="button" class="btn btn-outline-danger btn-sm remove-language" title="Remove this language">
                    Remove
                </button>
            </div>
        `;
        languagesContainer.appendChild(languageEntry);
    });

    languagesContainer.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-language')) {
            const entries = languagesContainer.querySelectorAll('.language-entry');
            if (entries.length > 1) {
                e.target.closest('.language-entry').remove();
            }
        }
    });
});
</script>
{% endblock %}
        }
    });
});
</script>
{% endblock %}
