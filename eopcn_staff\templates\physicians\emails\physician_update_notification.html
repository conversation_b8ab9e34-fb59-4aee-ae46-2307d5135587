<!DOCTYPE html>
<html>
<head>
    <title>Member Update for {{ physician.last_name }}, {{ physician.first_name }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        table { width:70%; border-collapse: separate; border-spacing: 2px; }
        td { padding: 5px; }
        .label { text-align: right; width: 15%; }
        .value { background-color: #DDEBF7; width: 55%; }
    </style>
</head>
<body>
    {% load custom_filters %}
    <h1>Primary Care Provider Update</h1>
    <table border="0" cellspacing="2" style="width:70%">
        <tr>
            <td class="label"{% if changes.last_name %} style="background-color:#fff8c6;"{% endif %}>Last Name:</td>
            <td class="value">
                {% if changes.last_name %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.last_name.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.last_name.new_value }}</span>
                {% else %}
                    {{ physician.last_name }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.first_name %} style="background-color:#fff8c6;"{% endif %}>First Name:</td>
            <td class="value">
                {% if changes.first_name %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.first_name.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.first_name.new_value }}</span>
                {% else %}
                    {{ physician.first_name }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.title %} style="background-color:#fff8c6;"{% endif %}>Title:</td>
            <td class="value">
                {% if changes.title %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.title.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.title.new_value }}</span>
                {% else %}
                    {{ physician.title }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.practitioner_id %} style="background-color:#fff8c6;"{% endif %}>Practitioner ID:</td>
            <td class="value">
                {% if changes.practitioner_id %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.practitioner_id.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.practitioner_id.new_value }}</span>
                {% else %}
                    {{ physician.practitioner_id }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.gender %} style="background-color:#fff8c6;"{% endif %}>Gender:</td>
            <td class="value">
                {% if changes.gender %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.gender.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.gender.new_value }}</span>
                {% else %}
                    {{ physician.gender }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.primary_email %} style="background-color:#fff8c6;"{% endif %}>Primary Email:</td>
            <td class="value">
                {% if changes.primary_email %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.primary_email.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.primary_email.new_value }}</span>
                {% else %}
                    {% if physician.primary_email %}
                        <a href="mailto:{{ physician.primary_email }}">{{ physician.primary_email }}</a>
                    {% else %}
                        None
                    {% endif %}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.primary_phone %} style="background-color:#fff8c6;"{% endif %}>Primary Phone:</td>
            <td class="value">
                {% if changes.primary_phone %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.primary_phone.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.primary_phone.new_value }}</span>
                {% else %}
                    {{ physician.primary_phone|phone_format }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.alternate_email %} style="background-color:#fff8c6;"{% endif %}>Alternate Email:</td>
            <td class="value">
                {% if changes.alternate_email %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.alternate_email.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.alternate_email.new_value }}</span>
                {% else %}
                    {% if physician.alternate_email %}
                        <a href="mailto:{{ physician.alternate_email }}">{{ physician.alternate_email }}</a>
                    {% else %}
                        None
                    {% endif %}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.alternate_phone %} style="background-color:#fff8c6;"{% endif %}>Alternate Phone:</td>
            <td class="value">
                {% if changes.alternate_phone %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.alternate_phone.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.alternate_phone.new_value }}</span>
                {% else %}
                    {{ physician.alternate_phone }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.do_not_email %} style="background-color:#fff8c6;"{% endif %}>Email Opt-out:</td>
            <td class="value">
                {% if changes.do_not_email %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.do_not_email.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.do_not_email.new_value }}</span>
                {% else %}
                    {% if physician.do_not_email %}Yes{% else %}No{% endif %}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.date_signed_eopcn %} style="background-color:#fff8c6;"{% endif %}>Date Signed (EOPCN):</td>
            <td class="value">
                {% if changes.date_signed_eopcn %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.date_signed_eopcn.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.date_signed_eopcn.new_value }}</span>
                {% else %}
                    {{ physician.date_signed_eopcn }}
                {% endif %}
            </td>
        </tr>
        <tr>
            <td class="label"{% if changes.date_left_eopcn %} style="background-color:#fff8c6;"{% endif %}>Date Left (EOPCN):</td>
            <td class="value">
                {% if changes.date_left_eopcn %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.date_left_eopcn.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.date_left_eopcn.new_value }}</span>
                {% else %}
                    {{ physician.date_left_eopcn }}
                {% endif %}
            </td>
        </tr>
        {% if physician.languages.all or changes.languages %}
        <tr>
            <td class="label"{% if changes.languages %} style="background-color:#fff8c6;"{% endif %}>Languages Spoken:</td>
            <td class="value">
                {% if changes.languages %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.languages.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.languages.new_value }}</span>
                {% else %}
                    {% if physician.languages.all %}
                        {% for language in physician.languages.all %}
                            {{ language.language }}{% if not forloop.last %}, {% endif %}
                        {% endfor %}
                    {% else %}
                        None specified
                    {% endif %}
                {% endif %}
            </td>
        </tr>
        {% endif %}
    </table>
    
    {% if physician.comment %}
    <h2>Additional Comments</h2>
    <table border="0" cellspacing="2" style="width:70%">
        <tr>
            <td class="label"{% if changes.comment %} style="background-color:#fff8c6;"{% endif %}>Comment:</td>
            <td class="value">
                {% if changes.comment %}
                    <span style="background-color: #ffebee; text-decoration: line-through;">{{ changes.comment.old_value }}</span>
                    → <span style="background-color: #e8f5e8; font-weight: bold;">{{ changes.comment.new_value }}</span>
                {% else %}
                    {{ physician.comment }}
                {% endif %}
            </td>
        </tr>
    </table>
    {% endif %}
    
    <br>
    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}
    <p>
        Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).
    </p>
    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>