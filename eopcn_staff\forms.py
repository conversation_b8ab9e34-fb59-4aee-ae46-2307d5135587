from django import forms
from django.forms.models import modelformset_factory, inlineformset_factory, BaseInlineFormSet
from .models import Clinic, Staff, StaffAllocation, StaffAssignment, Program, Service, Position, StaffSupervisor, StaffRole, StaffLeave, StaffLeaveTypes, StaffSupervisor, StaffAssignmentsInClinic, SeatingMap, StaffLocationContact, StaffCoverage, PositionList, Physician, ClinicPhysician, ClinicNote, EmailRecipient, EmailGroup, EmailGroupMembership, EmailRecipient, EmailGroup, EmailGroupMembership
from django.db.models import Q
from django.utils.timezone import now
from datetime import datetime, date, timedelta
from django.utils import timezone
import pytz



class AddStaffForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    # Define N95 mask size choices with specific model numbers
    N95_MASK_SIZES = [
        ('', '--Select N95 Mask Size--'),
        ('1804', '1804'),
        ('1804S', '1804S'),
        ('1860', '1860'),
        ('1860S', '1860S'),
        ('1870+', '1870+'),
        ('8110S', '8110S'),
        ('9210', '9210'),
        ('8210', '8210'),
    ]
    
    # Override the default CharField with a ChoiceField
    n95_mask_size = forms.ChoiceField(choices=N95_MASK_SIZES, required=False, label="N95 Mask Size")

    class Meta:
        model = Staff
        fields = [
            'first_name',
            'last_name',
            'photo',
            'start_date',
            'suggested_email',
            'end_date',
            'currently_active',
            'desk_number',
            'office_number',
            'computer_number',
            'phone',
            'ext',
            'n95_mask_size',
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'currently_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class StaffAssignmentForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    supervisor = forms.ModelChoiceField(
        queryset=StaffSupervisor.objects.select_related('staff').filter(currently_active=True).order_by('staff__first_name', 'staff__last_name'),
        label="Supervisor",
        required=False,
        empty_label="--Select Supervisor--"
    )

    role = forms.ModelChoiceField(queryset=StaffRole.objects.all().order_by('role_name'), label="Role", required=False)
    service = forms.ModelChoiceField(queryset=Service.objects.all().order_by('service_name'), label="Program", required=False)

    PERMANENT_VS_TEMPORARY_CHOICES = [
        ('Permanent', 'Permanent'),
        ('Temporary', 'Temporary'),
    ]
    permanent_vs_temporary = forms.ChoiceField(
        choices=PERMANENT_VS_TEMPORARY_CHOICES,
        widget=forms.RadioSelect,
        label="Employment Type", 
        required=False,
    )

    add_another_assignment = forms.BooleanField(
        required=False,
        label="Add another assignment",
        widget=forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')]),
    )

    position = forms.ModelChoiceField(
        queryset=Position.objects.none(),  # will set dynamically
        label="Position",
        required=False,  # or True, depending on your model
    )

    role = forms.ModelChoiceField(
        queryset=StaffRole.objects.none(),  # will set dynamically
        label="Role",
        required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Include only available positions
        available_position_ids = PositionList.objects.filter(
            is_available=1
        ).values_list('position_id', flat=True)

        qs = Position.objects.filter(position_id__in=available_position_ids).order_by('position_number')

        self.fields['position'].queryset = qs


        # If editing an existing assignment, include logic to keep the occupant’s current position, if needed
        assignment_instance = kwargs.get('instance', None)
        if assignment_instance and assignment_instance.position:
            current_position_id = assignment_instance.position.position_id
            self.fields['position'].queryset = (
                self.fields['position'].queryset
                | Position.objects.filter(position_id=current_position_id)
            ).distinct()

        role_queryset = StaffRole.objects.filter(
            end_date__isnull=True
        ) | StaffRole.objects.filter(
            end_date__gte=now().date()
        )

        # Ensure current role remains available if editing an existing assignment
        if assignment_instance and assignment_instance.role:
            current_role_id = assignment_instance.role.role_id
            role_queryset = role_queryset | StaffRole.objects.filter(role_id=current_role_id)

        self.fields['role'].queryset = role_queryset.distinct().order_by('role_name')
    

    class Meta:
        model = StaffAssignment
        fields = [
            'role',
            'supervisor',
            'position',
            'permanent_vs_temporary',
            'role_fte',
            'service',
            'start_date',
            'end_date',
            'currently_active',
            'add_another_assignment',
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'role_fte': forms.NumberInput(attrs={'step': '0.10', 'min': '0', 'max': '1'}),
            'permanent_vs_temporary': forms.RadioSelect,
            'add_another_assignment': forms.RadioSelect,
            'currently_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'role_fte': 'Total FTE',
        }

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
        
    #     # Check if we’re editing an existing assignment with a position
    #     if self.instance and self.instance.position:
    #         # Include the assigned position in the queryset, even if unavailable or decommissioned
    #         self.fields['position'].queryset = Position.objects.filter(
    #             (Q(is_available=True) & Q(decommissioned=False)) | Q(position_id=self.instance.position.position_id)
    #         ).order_by('position_number')
    #     else:
    #         # For new assignments, show only available and non-decommissioned positions
    #         self.fields['position'].queryset = Position.objects.filter(
    #             is_available=True,
    #             decommissioned=False
    #         ).order_by('position_number')


StaffAssignmentFormSet = inlineformset_factory(
    Staff,
    StaffAssignment,
    form=StaffAssignmentForm,
    extra=0,
    can_delete=True  # Allows users to delete assignments if needed
)


class StaffAllocationForm(forms.ModelForm):
    clinic = forms.ModelChoiceField(queryset=Clinic.objects.all().order_by('clinic_name'), label="Clinic", required=False)
    program = forms.ModelChoiceField(queryset=Program.objects.all().order_by('program_name'), label="EOPCN team:", required=False)

    # Dropdown options for assignment_in_clinic
    assignment_in_clinic = forms.ModelChoiceField(
        queryset=StaffAssignmentsInClinic.objects.all().order_by('assignment_name'),
        label="Assignment in clinic:",
        required=False,
        empty_label="--Select Assignment--"
    )

    # Adding a custom label and radio choices
    central_vs_decentral_choices = [
        ('RIC/Decentralized', 'RIC/Decentralized'),
        ('Centralized', 'Centralized'),
    ]
    centralized_vs_ric = forms.ChoiceField(
        choices=central_vs_decentral_choices,
        widget=forms.RadioSelect,
        label="Allocation type:",
        required=False
    )

    # Dropdown options for allocation_type
    allocation_type_choices = [
        ('In-Person', 'In-Person'),
        ('Project Based Support', 'Project Based Support'),
        ('Remote', 'Remote'),
        ('RIC Float', 'RIC Float'),
    ]

    allocation_type = forms.ChoiceField(
        choices=allocation_type_choices,
        widget=forms.RadioSelect,
        label="Delivery type:",
        required=False
    )

    class Meta:
        model = StaffAllocation
        fields = [
            'clinic',
            'program',
            'allocation_type',
            'centralized_vs_ric',
            'fte',
            'start_date',
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'assignment_in_clinic',
            'currently_active',
            'end_date',
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'fte': forms.NumberInput(attrs={'step': '0.10', 'min': '0', 'max': '1'}),
            'currently_active': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }
        labels = {
            'fte': 'FTE allocated',
            'monday': 'Mon',
            'tuesday': 'Tue',
            'wednesday': 'Wed',
            'thursday': 'Thu',
            'friday': 'Fri',
            'program': 'Service',
            'centralized_vs_ric': 'Allocation type',
        }


StaffAllocationFormSet = inlineformset_factory(
    StaffAssignment,
    StaffAllocation,
    form=StaffAllocationForm,
    extra=1,
)


class BaseStaffAssignmentFormSet(BaseInlineFormSet):
    def add_fields(self, form, index):
        super().add_fields(form, index)
        # Create an inline formset for the related allocations
        form.nested = StaffAllocationFormSet(
            instance=form.instance,
            data=form.data if form.is_bound else None,
            prefix='allocation-%s-%s' % (
                form.prefix,
                StaffAllocationFormSet.get_default_prefix()
            )
        )

    def is_valid(self):
        # Extend validation to include nested formset
        result = super().is_valid()
        for form in self.forms:
            if hasattr(form, 'nested'):
                result = result and form.nested.is_valid()
        return result

    def save(self, commit=True):
        # Save both the main formset and nested formsets
        result = super().save(commit=commit)
        for form in self.forms:
            if hasattr(form, 'nested'):
                if not self._should_delete_form(form):
                    form.nested.save(commit=commit)
        return result


# Formset for StaffAssignments with nested Allocations
StaffAssignmentWithAllocationsFormSet = inlineformset_factory(
    Staff,
    StaffAssignment,
    form=StaffAssignmentForm,
    formset=BaseStaffAssignmentFormSet,
    extra=0,
)


class StaffLeaveForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    leave_type = forms.ModelChoiceField(queryset=StaffLeaveTypes.objects.all().order_by('leave_type_name'), label="Leave Type", required=False)
    reminder_datetime = forms.DateTimeField(
        required=False,
        label="Reminder Date/Time (MST)",
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local'}),
        help_text=(
            "Set an optional reminder to verify the return date. "
            "Leave blank to remove any existing reminder."
        )
    )
    send_reminder_to_self = forms.BooleanField(
        required=False,
        label="Send reminder to me"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Convert reminder_datetime from UTC to MST for display
        if self.instance and self.instance.reminder_datetime:
            import pytz
            from django.utils import timezone

            mst = pytz.timezone('US/Mountain')
            # Convert UTC stored time to MST for display
            reminder_mst = self.instance.reminder_datetime.astimezone(mst)
            # Remove timezone info for datetime-local input
            self.initial['reminder_datetime'] = reminder_mst.replace(tzinfo=None)

        # Set send_reminder_to_self based on existing email address
        if self.instance and self.instance.reminder_email_address:
            self.initial['send_reminder_to_self'] = True
    reminder_email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Select Email Group--",
        required=False,
        label="Reminder Email Group",
        help_text="Choose a group to receive the reminder.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        tz = pytz.timezone('US/Mountain')
        if self.instance and self.instance.reminder_datetime:
            aware_dt = self.instance.reminder_datetime
            local_dt = aware_dt.astimezone(tz)
            self.initial['reminder_datetime'] = local_dt.strftime('%Y-%m-%dT%H:%M')
        if self.instance and self.instance.reminder_email_address:
            self.initial['send_reminder_to_self'] = True
    
    class Meta:
        model = StaffLeave
        fields = [
            'leave_type',
            'leave_start_date',
            'return_date',
            'reminder_datetime',
            'send_reminder_to_self',
            'reminder_email_group',
            # Exclude date_created, created_by, date_modified, modified_by
        ]
        widgets = {
            'leave_start_date': forms.DateInput(attrs={'type': 'date'}),
            'return_date': forms.DateInput(attrs={'type': 'date'}),
        }

class CommentForm(forms.Form):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=False, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )


class PositionForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    class Meta:
        model = Position
        fields = ['position_number', 'start_date', 'end_date'] 
        widgets = {
            'position_number': forms.TextInput(attrs={'placeholder': 'Enter position number'}),
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
        }



class StaffSupervisorForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    # Dropdown list of staff members from the Staff model
    staff = forms.ModelChoiceField(
        queryset=Staff.objects.all().order_by('first_name'),  # Order staff alphabetically
        label="Staff Member",
        empty_label="--Select Staff Member--"
    )

    class Meta:
        model = StaffSupervisor
        fields = ['staff', 'currently_active']
        widgets = {
            'currently_active': forms.CheckboxInput(),
        }


class StaffAssignmentsInClinicForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    class Meta:
        model = StaffAssignmentsInClinic
        fields = ['assignment_name']


class ServiceForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    class Meta:
        model = Service
        fields = ['service_name', 'division']
        labels = {
            'service_name': 'Service Name',
            'division': 'Division',
        }
        widgets = {
            'service_name': forms.TextInput(attrs={'placeholder': 'Enter Service Name'}),
            'division': forms.TextInput(attrs={'placeholder': 'Enter Division (optional)'}),
        }


class ProgramForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    service = forms.ModelChoiceField(
        queryset=Service.objects.all().order_by('service_name'),  # Order services alphabetically
        label="Service",
        empty_label="--Select Service--",
        required=False
    )

    class Meta:
        model = Program
        fields = ['program_name', 'service']
        labels = {
            'program_name': 'Program Name',
            'service': 'Service',
        }
        widgets = {
            'program_name': forms.TextInput(attrs={'placeholder': 'Enter Program Name'}),
        }


class StaffRoleForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    class Meta:
        model = StaffRole
        fields = ['role_name', 'ah_role_title', 'ah_role_category', 'start_date', 'end_date']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
        }


class SeatingMapForm(forms.ModelForm):
    class Meta:
        model = SeatingMap
        fields = ['file']


class StaffLocationContactForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    clinic = forms.ModelChoiceField(queryset=Clinic.objects.all().order_by('clinic_name'), label="Clinic", required=False)

    CONTACT_TYPE_CHOICES = [
        ('Desk Phone', 'Desk Phone'),
        ('Work Cell', 'Work Cell'),
        ('Personal Cell', 'Personal Cell'),
    ]

    contact_type = forms.ChoiceField(choices=CONTACT_TYPE_CHOICES, widget=forms.Select, label="Contact Type", required=False)

    class Meta:
        model = StaffLocationContact
        fields = [
            'contact_type', 
            'clinic', 
            'extension', 
            'office_number', 
            'phone', 
            'monday', 
            'tuesday', 
            'wednesday', 
            'thursday', 
            'friday', 
            'contact_notes'
        ]
        widgets = {
            'monday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'tuesday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'wednesday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'thursday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'friday': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'phone': 'Direct Phone',
            'extension': 'Extension',
            'contact_notes': 'Notes',
        }

class StaffCoverageForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    covering_staff = forms.ModelChoiceField(
        queryset=Staff.objects.all().filter(currently_active=True).order_by('first_name', 'last_name'),  # Order staff alphabetically
        required=False,
        label="Covering Staff",
        empty_label="--Select Staff Member--"
    )
    coverage_type_choices = [
        ('Position Replacement', 'Position Replacement'),
        ('Workload Coverage', 'Workload Coverage'),
    ]
    coverage_type = forms.ChoiceField(
        choices=coverage_type_choices,
        widget=forms.RadioSelect(attrs={'class': 'radio-inline'}),
        label="Coverage Type",
        required=False
    )
    
    # Add staff_coverage_id as a hidden field to ensure it's always included
    staff_coverage_id = forms.IntegerField(required=False, widget=forms.HiddenInput())
    
    class Meta:
        model = StaffCoverage
        fields = ['staff_coverage_id', 'covering_staff', 'coverage_type', 'coverage_start_date', 'coverage_end_date']
        widgets = {
            'coverage_start_date': forms.DateInput(attrs={'type': 'date'}),
            'coverage_end_date': forms.DateInput(attrs={'type': 'date'}),
        }


StaffCoverageFormSet = inlineformset_factory(
    StaffLeave,
    StaffCoverage,
    form=StaffCoverageForm,
    extra=1,
    can_delete=True
)

class PhysicianForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    # Explicitly define the boolean field

    # Add an extra field for sending an auto email.
    email_leadership = forms.BooleanField(required=False, label="Email leaders", initial=True, widget=forms.CheckboxInput())
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False, label="Comment")
    
    # Add checkbox for left EOPCN
    left_eopcn = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput,
        label="Left EOPCN"
    )
    
    # Modified to be a BooleanField but will save as "Yes" in the database
    no_longer_practicing = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput,
        label="No Longer Practicing"
    )
    
    # Add the do_not_email field
    do_not_email = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput,
        label="Email Opt-out",
        help_text="Check to opt out of email communications"
    )
    
    reason_for_leaving = forms.ChoiceField(
         choices=[
            ("Retiring from practice", "Retiring from practice"),
            ("Taking an extended leave from practice", "Taking an extended leave from practice"),
            ("Practicing outside of Alberta", "Practicing outside of Alberta"),
            ("Not sure, but no longer in a member clinic", "Not sure, but no longer in a member clinic")
         ],
         widget=forms.RadioSelect,
         required=False,
         label="Reason for Leaving"
    )
    date_no_longer_practicing = forms.DateField(required=False, widget=forms.DateInput(attrs={'type': 'date'}), label="Date No Longer Practicing")
    
    # Define gender as a ChoiceField with a Select widget
    GENDER_CHOICES = [
        ('', '--Select Gender--'),
        ('Male', 'Male'),
        ('Female', 'Female'),
        ('Non-binary', 'Non-binary'),
        ('Prefer not to say', 'Prefer not to say')
    ]
    
    gender = forms.ChoiceField(
        choices=GENDER_CHOICES,
        required=False,
        widget=forms.Select(),
        label="Gender"
    )
    
    # Add separate fields for first and last name
    first_name = forms.CharField(max_length=255, required=False, label="First Name")
    last_name = forms.CharField(max_length=255, required=False, label="Last Name")
    
    class Meta:
        model = Physician
        fields = [
            'physician_name',
            'first_name',    # Added first name
            'last_name',     # Added last name
            'title',
            'practitioner_id',
            'gender',
            'primary_email',
            'primary_phone',
            'alternate_email',
            'alternate_phone',
            'do_not_email',
            'date_signed_eopcn',
            'date_left_eopcn',
            'no_longer_practicing',
            'reason_for_leaving',
            'date_no_longer_practicing',
            'comment'
        ]
        widgets = {
            'title': forms.RadioSelect(
                choices=[('Dr.', 'Dr.'), ('NP', 'NP')],
                attrs={'class': 'radio-inline'}
            ),
            'date_signed_eopcn': forms.DateInput(attrs={'type': 'date'}),
            'date_left_eopcn': forms.DateInput(attrs={'type': 'date'}),
            'primary_phone': forms.NumberInput(),
            'alternate_phone': forms.NumberInput(),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize checkbox state based on stored value
        if self.instance and self.instance.no_longer_practicing == "Yes":
            self.fields['no_longer_practicing'].initial = True
            
        # Initialize left_eopcn checkbox based on date_left_eopcn value
        if self.instance and self.instance.date_left_eopcn:
            self.fields['left_eopcn'].initial = True
            
        # Initialize first_name and last_name from physician_name if they are not already set
        if self.instance and self.instance.physician_name and (not self.instance.first_name or not self.instance.last_name):
            # Check if physician_name has a comma
            if ', ' in self.instance.physician_name:
                last_name, first_name = self.instance.physician_name.split(', ', 1)
                self.fields['last_name'].initial = last_name.strip()
                self.fields['first_name'].initial = first_name.strip()
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        # Convert boolean to "Yes" if checked
        if self.cleaned_data.get('no_longer_practicing'):
            instance.no_longer_practicing = "Yes"
        else:
            instance.no_longer_practicing = None
            # Clear dependent fields if not checked
            instance.reason_for_leaving = None
            instance.date_no_longer_practicing = None
            
        # Clear date_left_eopcn if left_eopcn is not checked
        if not self.cleaned_data.get('left_eopcn'):
            instance.date_left_eopcn = None
            
        # Update first_name and last_name fields
        instance.first_name = self.cleaned_data.get('first_name')
        instance.last_name = self.cleaned_data.get('last_name')
        
        # Update physician_name to maintain the "Last Name, First Name" format
        if instance.first_name and instance.last_name:
            instance.physician_name = f"{instance.last_name}, {instance.first_name}"
            
        if commit:
            instance.save()
        return instance


class ClinicPhysicianForm(forms.ModelForm):
    # Render the clinic field as a dropdown ordered alphabetically.
    clinic = forms.ModelChoiceField(
        queryset=Clinic.objects.all().order_by('clinic_name'),
        label="Clinic Name",
        required=True
    )
    # Override the char fields to render as choice fields (radio buttons).
    portion_of_practice = forms.ChoiceField(
        choices=[('Primary', 'Primary'), ('Alternate', 'Alternate')],
        widget=forms.RadioSelect(attrs={'class': 'radio-inline'}),
        label="Portion of Practice"
    )
    accepting_patients = forms.ChoiceField(
        choices=[('Yes', 'Yes'), ('No', 'No')],
        widget=forms.RadioSelect(attrs={'class': 'radio-inline'}),
        label="Accepting Patients",
        required=False
    )
    include_on_afad_website = forms.ChoiceField(
        choices=[('Yes', 'Yes'), ('No', 'No')],
        widget=forms.RadioSelect(attrs={'class': 'radio-inline'}),
        label="Include on AFAD",
        required=False
    )
    include_on_eopcn_website = forms.ChoiceField(
        choices=[('Yes', 'Yes'), ('No', 'No')],
        widget=forms.RadioSelect(attrs={'class': 'radio-inline'}),
        label="Include on EOPCN",
        required=False
    )
    
    # Add the three new fields
    CPAR_Panel_ID = forms.IntegerField(
        required=False,
        widget=forms.NumberInput(attrs={'class': 'form-control'}),
        label="CPAR Panel ID",
        help_text="Enter the CPAR Panel ID number"
    )
    
    active_CII = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label="Active CII"
    )
    
    active_CPAR = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label="Active CPAR"
    )

    class Meta:
        model = ClinicPhysician
        fields = [
            'clinics_physicians_ID',
            'clinic',
            'portion_of_practice',
            'accepting_patients',
            'include_on_afad_website',
            'include_on_eopcn_website',
            'date_active_in_clinic',
            'date_left_clinic',
            'CPAR_Panel_ID',
            'active_CII',
            'active_CPAR',
        ]
        widgets = {
            'clinics_physicians_ID': forms.HiddenInput(),
            'date_active_in_clinic': forms.DateInput(attrs={'type': 'date'}),
            'date_left_clinic': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If an existing instance has a clinic_id, pre-select the Clinic object.
        if self.instance and self.instance.clinic_id:
            try:
                clinic_obj = Clinic.objects.get(clinic_id=self.instance.clinic_id)
                self.fields['clinic'].initial = clinic_obj
            except Clinic.DoesNotExist:
                pass

    def save(self, commit=True):
        instance = super().save(commit=False)
        selected_clinic = self.cleaned_data.get('clinic')
        if selected_clinic:
            instance.clinic_id = selected_clinic.clinic_id
            instance.clinic_name = selected_clinic.clinic_name
        if commit:
            instance.save()
        return instance

class ClinicForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )

    # Dropdown options for med_group_or_site
    MED_GROUP_CHOICES = [
        ('', '--Select Medical Group/Site--'),
        ('None Identified', 'None Identified'),
        ('Academic', 'Academic'),
        ('Alberta Hospital, Building 10', 'Alberta Hospital, Building 10'),
        ('Boyle McCauley Health Centre', 'Boyle McCauley Health Centre'),
        ('EMG', 'EMG'),
        ('EOPCN', 'EOPCN'),
        ('Kaye Edmonton Clinic', 'Kaye Edmonton Clinic'),
        ('Kia Medigroup', 'Kia Medigroup'),
        ('MD Docs', 'MD Docs'),
        ('Medicine Place', 'Medicine Place'),
        ('Medigroup', 'Medigroup'),
        ('Misericordia Hospital, Cabrini Centre', 'Misericordia Hospital, Cabrini Centre'),
        ('Northeast Community Health Centre', 'Northeast Community Health Centre'),
        ('Private Practice', 'Private Practice'),
        ('Pro Care', 'Pro Care'),
        ('Royal Alexandra Hospital, Anderson Hall', 'Royal Alexandra Hospital, Anderson Hall'),
        ('Royal Alexandra Hospital, Community Services Center', 'Royal Alexandra Hospital, Community Services Center'),
        ('University of Alberta, Clinical Sciences Building', 'University of Alberta, Clinical Sciences Building'),
        ('University of Alberta, Students\' Union Building', 'University of Alberta, Students\' Union Building'),
    ]
    
    # Dropdown options for city
    CITY_CHOICES = [
        ('', '--Select City--'),
        ('Edmonton', 'Edmonton'),
        ('St. Albert', 'St. Albert'),
        ('Sherwood Park', 'Sherwood Park'),
        ('Fort Saskatchewan', 'Fort Saskatchewan'),
        ('Spruce Grove', 'Spruce Grove'),
        ('Leduc', 'Leduc'),
        ('Beaumont', 'Beaumont'),
        ('Stony Plain', 'Stony Plain'),
        ('Morinville', 'Morinville'),
        ('Devon', 'Devon'),
        ('Gibbons', 'Gibbons'),
        ('Bon Accord', 'Bon Accord'),
    ]
    
    # Dropdown options for clinic_emr
    EMR_CHOICES = [
        ('', '--Select EMR System--'),
        ('Accuro', 'Accuro'),
        ('Ava', 'Ava'),
        ('ConnectCare', 'ConnectCare'),
        ('eClinician', 'eClinician'),
        ('Healthquest', 'Healthquest'),
        ('Input Health', 'Input Health'),
        ('Juno', 'Juno'),
        ('Med Access', 'Med Access'),
        ('Paper based', 'Paper based'),
        ('Telus CHR', 'Telus CHR'),
        ('Wolf', 'Wolf'),
    ]
    
    # Dropdown options for contact roles
    CONTACT_ROLE_CHOICES = [
        ('', '--Select Role--'),
        ('Clinic Manager', 'Clinic Manager'),
        ('Manager Assistant', 'Manager Assistant'),
        ('Medical Office Assistant', 'Medical Office Assistant'),
        ('Operations for Med Group or Site', 'Operations for Med Group or Site'),
        ('Owner(s)', 'Owner(s)'),
        ('Physician', 'Physician'),
        ('Program Manager', 'Program Manager'),
        ('Quality Coordinator', 'Quality Coordinator'),
        ('Supervisor', 'Supervisor'),
    ]
    
    # Dropdown options for province
    PROVINCE_CHOICES = [
        ('', '--Select Province--'),
        ('Alberta', 'Alberta'),
        ('British Columbia', 'British Columbia'),
        ('Saskatchewan', 'Saskatchewan'),
    ]
    
    # Convert fields to choice fields
    med_group_or_site = forms.ChoiceField(
        choices=MED_GROUP_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    city = forms.ChoiceField(
        choices=CITY_CHOICES,
        required=False,
        initial='Edmonton',  # Set Edmonton as the default
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    clinic_emr = forms.ChoiceField(
        choices=EMR_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    primary_contact_role = forms.ChoiceField(
        choices=CONTACT_ROLE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    alternate_contact_role = forms.ChoiceField(
        choices=CONTACT_ROLE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    province = forms.ChoiceField(
        choices=PROVINCE_CHOICES,
        required=False,
        initial='Alberta',  # Set Alberta as the default
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    # Add explicit field definitions for contact names to apply form-control class
    primary_contact_first_name = forms.CharField(required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    primary_contact_last_name = forms.CharField(required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    alternate_contact_first_name = forms.CharField(required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    alternate_contact_last_name = forms.CharField(required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))

    class Meta:
        model = Clinic
        fields = [
            'clinic_name', 'med_group_or_site', 'street_address', 'floor_unit_room',
            'city', 'province', 'postal_code', 'business_phone', 'extension',
            'fax', 'clinic_website', 'clinic_emr', 'pia_number',
            'include_on_eopcn_website', 
            'primary_contact_first_name', 'primary_contact_last_name', # Added
            'primary_contact_role',
            'primary_contact_phone', 'primary_contact_ext', 'primary_contact_email',
            'alternate_contact_first_name', 'alternate_contact_last_name', # Added
            'alternate_contact_role', 'alternate_contact_phone',
            'alternate_contact_ext', 'alternate_contact_email'
            # Removed 'primary_contact' and 'alternate_contact' as they are split into first/last name
        ]
        widgets = {
            'clinic_name': forms.TextInput(attrs={'class': 'form-control'}),
            'street_address': forms.TextInput(attrs={'class': 'form-control'}),
            'floor_unit_room': forms.TextInput(attrs={'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'class': 'form-control'}),
            'business_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'extension': forms.TextInput(attrs={'class': 'form-control'}),
            'fax': forms.TextInput(attrs={'class': 'form-control'}),
            'clinic_website': forms.URLInput(attrs={'class': 'form-control'}),
            'pia_number': forms.TextInput(attrs={'class': 'form-control'}),
            'include_on_eopcn_website': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            # 'primary_contact' and 'alternate_contact' widgets removed as fields are now split
            'primary_contact_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'primary_contact_ext': forms.TextInput(attrs={'class': 'form-control'}),
            'primary_contact_email': forms.EmailInput(attrs={'class': 'form-control'}),
            'alternate_contact_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'alternate_contact_ext': forms.TextInput(attrs={'class': 'form-control'}),
            'alternate_contact_email': forms.EmailInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set initial values from instance if editing
        if self.instance and self.instance.pk:
            # For med_group_or_site dropdown
            if self.instance.med_group_or_site and self.instance.med_group_or_site not in dict(self.MED_GROUP_CHOICES).keys():
                # Add the existing value if it's not in the choices
                self.fields['med_group_or_site'].choices += [(self.instance.med_group_or_site, self.instance.med_group_or_site)]
            
            # For city dropdown
            if self.instance.city and self.instance.city not in dict(self.CITY_CHOICES).keys():
                self.fields['city'].choices += [(self.instance.city, self.instance.city)]
            
            # For clinic_emr dropdown
            if self.instance.clinic_emr and self.instance.clinic_emr not in dict(self.EMR_CHOICES).keys():
                self.fields['clinic_emr'].choices += [(self.instance.clinic_emr, self.instance.clinic_emr)]
            
            # For primary_contact_role dropdown
            if self.instance.primary_contact_role and self.instance.primary_contact_role not in dict(self.CONTACT_ROLE_CHOICES).keys():
                self.fields['primary_contact_role'].choices += [(self.instance.primary_contact_role, self.instance.primary_contact_role)]
            
            # For alternate_contact_role dropdown
            if self.instance.alternate_contact_role and self.instance.alternate_contact_role not in dict(self.CONTACT_ROLE_CHOICES).keys():
                self.fields['alternate_contact_role'].choices += [(self.instance.alternate_contact_role, self.instance.alternate_contact_role)]
            
            # For province dropdown
            if self.instance.province and self.instance.province not in dict(self.PROVINCE_CHOICES).keys():
                self.fields['province'].choices += [(self.instance.province, self.instance.province)]


class ClinicNoteForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    # Define entry type choices
    TYPE_CHOICES = [
        ('Email', 'Email'),
        ('Clinic Visit', 'Clinic Visit'),
        ('Phone Call', 'Phone Call'),
        ('EOPCN Visit', 'EOPCN Visit'),
        ('EOPCN Internal', 'EOPCN Internal'),
        ('Future Projects', 'Future Projects'),
        ('Other', 'Other'),
    ]
    
    # Override the type_of_entry field to use dropdown
    type_of_entry = forms.ChoiceField(
        choices=TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-control'}),
    )
    
    # Define the date field as a simple date (no time)
    date_of_entry = forms.DateField(
        widget=forms.DateInput(
            attrs={'class': 'form-control', 'type': 'date'},
            format='%Y-%m-%d'
        ),
        required=False
    )
    
    # Add physicians_in_attendance as a separate field (not a model field)
    physicians_in_attendance = forms.ModelMultipleChoiceField(
        queryset=Physician.objects.none(),
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label="Physicians in Attendance"
    )
    
    other_attendees = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 2, 'class': 'form-control'}),
        required=False
    )
    
    class Meta:
        model = ClinicNote
        fields = ['date_of_entry', 'author_of_entry', 'type_of_entry', 'note']
        widgets = {
            'author_of_entry': forms.TextInput(attrs={'class': 'form-control'}),
            'note': forms.Textarea(attrs={'rows': 8, 'class': 'form-control'}),
        }
        
    def __init__(self, *args, **kwargs):
        clinic = kwargs.pop('clinic', None)
        super().__init__(*args, **kwargs)
        
        # Limit physicians to those associated with this clinic
        if clinic:
            # Get physicians associated with this clinic (both active and inactive)
            clinic_physician_ids = ClinicPhysician.objects.filter(
                clinic=clinic
            ).values_list('physician__physician_id', flat=True)
            
            # Set the queryset to include all physicians associated with this clinic
            self.fields['physicians_in_attendance'].queryset = Physician.objects.filter(
                physician_id__in=clinic_physician_ids
            ).order_by('last_name', 'first_name')
            
            # Store clinic physicians for template access
            self.clinic_physicians = ClinicPhysician.objects.filter(
                clinic=clinic
            ).select_related('physician').order_by('physician__last_name', 'physician__first_name')
        
        # Handle initial data for physicians_in_attendance when editing
        if self.instance and self.instance.pk:
            # Get existing physician attendances for this note
            try:
                # Try different possible model names for physician attendance
                existing_physician_ids = []
                
                # Check if there's a related field for physician attendances
                if hasattr(self.instance, 'physician_attendances'):
                    existing_physician_ids = list(
                        self.instance.physician_attendances.values_list('physician__physician_id', flat=True)
                    )
                elif hasattr(self.instance, 'clinicnotephysicianattendance_set'):
                    existing_physician_ids = list(
                        self.instance.clinicnotephysicianattendance_set.values_list('physician__physician_id', flat=True)
                    )
                
                # If we found physician IDs, set them as initial values
                if existing_physician_ids:
                    self.initial['physicians_in_attendance'] = existing_physician_ids
                    print(f"Setting initial physicians: {existing_physician_ids}")  # Debug line
                    
            except Exception as e:
                print(f"Error setting initial physicians: {e}")
                self.initial['physicians_in_attendance'] = []


class PhysicianNameMappingForm(forms.Form):
    physician_id = forms.IntegerField(
        required=False,
        widget=forms.Select(choices=[]),
        label="Physician ID",
        help_text="Select the physician if known, or leave blank"
    )
    ah_full_name = forms.CharField(
        max_length=255,
        label="AH Full Name",
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Smith, John'
        }),
        help_text="The physician name as it appears in the AH import file"
    )
    eopcn_full_name = forms.CharField(
        max_length=255,
        label="EOPCN Full Name",
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Smith, Dr. John',
            'readonly': True,
            'style': 'background-color: #f5f5f5; color: #666;'
        }),
        help_text="The physician name as it appears in EOPCN database (auto-populated)"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Populate physician choices
        from django.db import connection
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT physician_id, physician_name, first_name, last_name
                    FROM mh_physicians
                    WHERE physician_id IS NOT NULL
                    ORDER BY last_name, first_name
                """)
                physicians = cursor.fetchall()
                
            physician_choices = [('', 'Select a physician (optional)')]
            for physician in physicians:
                # Use the display name logic: prefer first_name + last_name, fallback to physician_name
                if physician[2] and physician[3]:  # first_name and last_name exist
                    display_name = f"{physician[3]}, {physician[2]}"  # Last, First
                else:
                    display_name = physician[1] or "Unknown"  # physician_name fallback
                
                physician_choices.append((
                    physician[0],  # physician_id
                    f"{display_name} (ID: {physician[0]})"
                ))
                
            self.fields['physician_id'].widget.choices = physician_choices
            
        except Exception as e:
            print(f"Error loading physicians: {e}")
            self.fields['physician_id'].widget.choices = [
                ('', 'Error loading physicians - please try again')
            ]


# Email Management Forms
class EmailRecipientForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    class Meta:
        model = EmailRecipient
        fields = ['email', 'name', 'is_active']
        widgets = {
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class EmailGroupForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    class Meta:
        model = EmailGroup
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class EmailGroupMembershipForm(forms.ModelForm):
    comment = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    email_leadership = forms.BooleanField(required=False, initial=True, label="Email Team")
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Use Default Email Group--",
        required=False,
        label="Email Group",
        help_text="Select which email group should receive this notification. Leave blank to use the default email group.",
        widget=forms.Select(attrs={
            'class': 'form-control email-group-selector',
            'data-toggle': 'email-group-info'
        })
    )
    
    class Meta:
        model = EmailGroupMembership
        fields = ['recipient']
        widgets = {
            'recipient': forms.Select(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        group = kwargs.pop('group', None)
        super().__init__(*args, **kwargs)
        
        if group:
            # Only show recipients that are not already in this group
            existing_recipients = EmailGroupMembership.objects.filter(group=group).values_list('recipient_id', flat=True)
            self.fields['recipient'].queryset = EmailRecipient.objects.filter(
                is_active=True
            ).exclude(recipient_id__in=existing_recipients).order_by('name')
        else:
            self.fields['recipient'].queryset = EmailRecipient.objects.filter(is_active=True).order_by('name')


# Form for selecting email groups in other forms
class EmailGroupSelectionForm(forms.Form):
    email_group = forms.ModelChoiceField(
        queryset=EmailGroup.objects.filter(is_active=True).order_by('name'),
        empty_label="--Select Email Group--",
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )
