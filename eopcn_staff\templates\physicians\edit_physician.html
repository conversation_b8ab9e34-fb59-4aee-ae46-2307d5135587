{% extends "base.html" %}
{% load static %}

{% block title %}Edit {{ physician.last_name }}, {{ physician.first_name }}{% endblock %}

{% block content %}
<link rel="stylesheet" type="text/css" href="{% static 'eopcn_staff/css/edit_styles.css' %}">

<div class="form-container">
    <h2 class="subheading">Edit {{ physician.title }} {{ physician.last_name }}, {{ physician.first_name }}</h2>
    <form method="post">
        {% csrf_token %}

        <!-- Primary Care Provider Information Section -->
        <h3>Primary Care Provider Information</h3>
        <hr>
        {% for field in form.visible_fields %}
            {% if field.name != 'email_leadership' and field.name != 'reason_for_leaving' and field.name != 'date_no_longer_practicing' and field.name != 'comment' and field.name != 'email_group' and field.name != 'date_left_eopcn' and field.name != 'physician_name' and field.name != 'do_not_email' %}
            <div class="form-group">
                <div class="label-container">
                    {{ field.label_tag }}
                </div>
                <div class="field-container">
                    {{ field }}
                    {% if field.help_text %}
                        <small class="form-text text-muted">{{ field.help_text }}</small>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        {% endfor %}
        
        <!-- Email Opt-out Field -->
        <div class="form-group">
            <div class="label-container">
                <label>Email Opt-out:</label>
            </div>
            <div class="field-container">
                <input type="checkbox" 
                       name="do_not_email" 
                       id="id_do_not_email"
                       {% if form.do_not_email.value %}checked{% endif %}>
                <label for="id_do_not_email" style="margin-left: 8px; font-weight: normal;">Check to opt out of email communications</label>
            </div>
        </div>

        <!-- Languages Section -->
        <div class="form-group">
            <label for="languages">Languages Spoken:</label>
            <div id="languages-container">
                {% if physician.languages.all %}
                    {% for language in physician.languages.all %}
                    <div class="language-entry">
                        <div class="language-input-row">
                            <input type="text" class="form-control language-input" name="languages[]" value="{{ language.language }}">
                            <input type="hidden" name="language_ids[]" value="{{ language.physicians_languages_id }}">
                            <button type="button" class="btn btn-outline-danger btn-sm remove-language" title="Remove this language">
                                Remove
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="language-entry">
                        <div class="language-input-row">
                            <input type="text" class="form-control language-input" name="languages[]" placeholder="Enter language (e.g., English, Spanish, French)">
                            <button type="button" class="btn btn-outline-danger btn-sm remove-language" title="Remove this language">
                                Remove
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
            <div class="add-button-container">
                <button type="button" class="btn btn-success btn-sm" id="add-language" title="Add another language">
                    + Add Another Language
                </button>
            </div>
        </div>
        
        <!-- Conditional fields for Left EOPCN -->
        <div id="left-eopcn-details" style="display:none; margin-top: 20px; padding: 15px; border-left: 4px solid #dc3545; background-color: #f8f9fa;">
            <h4 style="color: #dc3545; margin-bottom: 15px;">EOPCN Departure Details</h4>
            <div class="form-group">
                <label for="id_date_left_eopcn">Date Left EOPCN:</label>
                {{ form.date_left_eopcn }}
                <small class="form-text" style="color:#dc3545;">
                    Note: Updating this field ends the physician's membership and automatically end-dates all associated clinic assignments.
                </small>
            </div>
        </div>
        
        <!-- Conditional fields for No Longer Practicing -->
        <div id="leaving-practice-details" style="display:none; margin-top: 20px; padding: 15px; border-left: 4px solid #007bff; background-color: #f8f9fa;">
            <h4 style="color: #007bff; margin-bottom: 15px;">No Longer Practicing Details</h4>
            <div class="form-group">
                {{ form.reason_for_leaving.label_tag }}
                {{ form.reason_for_leaving }}
            </div>
            <div class="form-group">
                {{ form.date_no_longer_practicing.label_tag }}
                {{ form.date_no_longer_practicing }}
                <small class="form-text" style="color:#007bff;">
                    Note: This field tracks when the primary care provider stopped practicing, but does not end their EOPCN membership.
                </small>
            </div>
        </div>
    </div>

    <!-- Email Group Widget -->
    <div class="form-container">
        {% with form=form %}
            {% include 'staff/email_group_widget.html' %}
        {% endwith %}
    </div>

    <!-- Save/Cancel Buttons -->
    <div class="form-container">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <button type="submit" class="btn btn-primary">Save Changes</button>
            <a href="{% url 'edit_physician_clinics' physician.physician_id %}" class="btn-secondary">Edit Clinic Assignments</a>
        </div>
    </div>
    </form>
</div>

<!-- JavaScript to toggle conditional fields -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // No Longer Practicing section
        var noPracticingCheckbox = document.getElementById('id_no_longer_practicing');
        var practicingDetailsDiv = document.getElementById('leaving-practice-details');
        
        // Left EOPCN section
        var leftEopcnCheckbox = document.getElementById('id_left_eopcn');
        var leftEopcnDetailsDiv = document.getElementById('left-eopcn-details');
        
        // Function to toggle visibility of No Longer Practicing section
        function togglePracticingDetails() {
            if (noPracticingCheckbox.checked) {
                practicingDetailsDiv.style.display = 'block';
            } else {
                practicingDetailsDiv.style.display = 'none';
            }
        }
        
        // Function to toggle visibility of Left EOPCN section
        function toggleLeftEopcnDetails() {
            if (leftEopcnCheckbox.checked) {
                leftEopcnDetailsDiv.style.display = 'block';
            } else {
                leftEopcnDetailsDiv.style.display = 'none';
            }
        }
        
        // Set initial states
        togglePracticingDetails();
        toggleLeftEopcnDetails();
        
        // Add event listeners for checkbox changes
        noPracticingCheckbox.addEventListener('change', togglePracticingDetails);
        leftEopcnCheckbox.addEventListener('change', toggleLeftEopcnDetails);
        
        // Languages functionality
        const addLanguageBtn = document.getElementById('add-language');
        const languagesContainer = document.getElementById('languages-container');

        addLanguageBtn.addEventListener('click', function() {
            const languageEntry = document.createElement('div');
            languageEntry.className = 'language-entry';
            languageEntry.innerHTML = `
                <div class="language-input-row">
                    <input type="text" class="form-control language-input" name="languages[]" placeholder="Enter language (e.g., English, Spanish, French)">
                    <button type="button" class="btn btn-outline-danger btn-sm remove-language" title="Remove this language">
                        Remove
                    </button>
                </div>
            `;
            languagesContainer.appendChild(languageEntry);
        });

        languagesContainer.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-language')) {
                const entries = languagesContainer.querySelectorAll('.language-entry');
                if (entries.length > 1) {
                    e.target.closest('.language-entry').remove();
                }
            }
        });
    });
</script>

<style>
    /* Languages section styling */
    .language-entry {
        margin-bottom: 10px;
    }

    .language-input-row {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .language-input {
        flex: 1;
        min-width: 0;
    }

    .remove-language {
        flex-shrink: 0;
        padding: 6px 12px;
        font-size: 0.875em;
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .remove-language:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .add-button-container {
        display: block;
        width: 100%;
        margin-top: 15px;
        clear: both;
        overflow: hidden;
    }

    #add-language {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
        padding: 8px 16px;
        font-size: 0.875em;
        display: block;
        width: auto;
        float: none;
        clear: both;
    }

    #add-language:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }
</style>
{% endblock %}
