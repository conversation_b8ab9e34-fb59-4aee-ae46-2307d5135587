<!DOCTYPE html>
<html>
<head>
    <title>Clinic Associations Updated for {{ physician.physician_name }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        table { width:70%; border-collapse: separate; border-spacing: 2px; }
        td { padding: 5px; }
        .label { text-align: right; width: 15%; }
        .value { background-color: #DDEBF7; width: 55%; }
    </style>
</head>
<body>
    <h1>Clinic Associations Update</h1>
    <p>This email details the clinic associations for primary care provider: <strong>{{ physician.last_name }}, {{ physician.first_name }}</strong>.</p>
    <table border="0" cellspacing="2" style="width:70%">
        {% for item in clinic_physicians %}
            {% if item.clinic and item.clinic.clinic_name %}
            <tr>
                <td class="label">Clinic Name:</td>
                <td class="value">{{ item.clinic.clinic_name }}</td>
            </tr>
            <tr>
                <td class="label">Portion of Practice:</td>
                <td class="value">{{ item.portion_of_practice }}</td>
            </tr>
            <tr>
                <td class="label">Accepting Patients:</td>
                <td class="value">{{ item.accepting_patients }}</td>
            </tr>
            <tr>
                <td class="label">Include on AFAD:</td>
                <td class="value">{{ item.include_on_afad_website }}</td>
            </tr>
            <tr>
                <td class="label">Include on EOPCN:</td>
                <td class="value">{{ item.include_on_eopcn_website }}</td>
            </tr>
            <tr>
                <td class="label">Date Active:</td>
                <td class="value">{{ item.date_active_in_clinic }}</td>
            </tr>
            <tr>
                <td class="label">Date Left:</td>
                <td class="value">{{ item.date_left_clinic }}</td>
            </tr>
            <tr>
                <td colspan="2"><hr></td>
            </tr>
            {% endif %}
        {% endfor %}
    </table>
    
    {% if comment %}
    <h2>Additional Comments</h2>
    <table border="0" cellspacing="2" style="width:70%">
        <tr>
            <td class="label">Comment:</td>
            <td class="value">{{ comment }}</td>
        </tr>
    </table>
    {% endif %}
    
    <br>
    <p>The above clinic associations have been updated automatically for {{ physician.last_name }}, {{ physician.first_name }}.</p>
    {% if details_url %}
    <p>
        <a href="{{ details_url }}">View record in operational database</a>
    </p>
    {% endif %}
    <p>Submitted by: <strong>{{ user.full_name }}</strong> ({{ user.email }}).</p>
    <p>Best regards,</p>
    <p>EOPCN Automation Admin</p>
</body>
</html>